// app.js
App({
  onLaunch() {
    console.log('中国国家博物馆小程序启动')
  },
  globalData: {
    userInfo: null
  }
})

// app.json
{
  "pages": [
    "pages/index/index",
    "pages/exhibition/exhibition",
    "pages/visit/visit",
    "pages/guide/guide"
  ],
  "window": {
    "backgroundTextStyle": "light",
    "navigationBarBackgroundColor": "#8B1538",
    "navigationBarTitleText": "中国国家博物馆",
    "navigationBarTextStyle": "white",
    "backgroundColor": "#f5f5f5"
  },
  "tabBar": {
    "color": "#999999",
    "selectedColor": "#8B1538",
    "backgroundColor": "#ffffff",
    "borderStyle": "black",
    "list": [
      {
        "pagePath": "pages/index/index",
        "iconPath": "images/home.png",
        "selectedIconPath": "images/home-active.png",
        "text": "展览"
      },
      {
        "pagePath": "pages/guide/guide",
        "iconPath": "images/guide.png",
        "selectedIconPath": "images/guide-active.png",
        "text": "导览"
      },
      {
        "pagePath": "pages/visit/visit",
        "iconPath": "images/service.png",
        "selectedIconPath": "images/service-active.png",
        "text": "便民"
      }
    ]
  },
  "style": "v2",
  "sitemapLocation": "sitemap.json"
}

// app.wxss
page {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  background-color: #f5f5f5;
}

.container {
  padding: 0;
  background: #f5f5f5;
}

/* 通用样式 */
.btn-primary {
  background: linear-gradient(135deg, #8B1538, #A91B47);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 16px;
}

.btn-primary:active {
  opacity: 0.8;
}

/* pages/index/index.wxml */
<view class="container">
  <!-- 自定义头部 -->
  <view class="custom-header">
    <view class="header-left">
      <view class="user-icon">👤</view>
      <view class="expand-icon">⌐</view>
    </view>
    <view class="museum-title">
      <text class="title-main">中国国家博物馆</text>
      <text class="title-sub">NATIONAL MUSEUM OF CHINA</text>
    </view>
    <view class="header-right">
      <view class="menu-dots">⋯</view>
      <view class="help-icon">?</view>
    </view>
  </view>

  <!-- 主展示区 -->
  <view class="hero-section">
    <swiper 
      class="book-swiper" 
      indicator-dots="true" 
      indicator-color="rgba(255,255,255,0.4)"
      indicator-active-color="rgba(255,255,255,0.9)"
      autoplay="true"
      interval="4000"
      circular="true">
      <swiper-item>
        <view class="book-display">
          <view class="book-cover">
            <text class="book-title">跟我去博</text>
            <text class="book-subtitle-small">Ancient China</text>
            <view class="book-content">
              <text class="book-number">115件</text>
              <text class="book-desc">文物里的</text>
              <text class="book-main-title">古代\n中国</text>
            </view>
            <view class="book-artifacts">
              <view class="artifact-row">
                <view class="artifact-image">鼎</view>
                <view class="artifact-image">器</view>
              </view>
            </view>
          </view>
        </view>
      </swiper-item>
      <!-- 可以添加更多轮播页面 -->
    </swiper>
  </view>

  <!-- 导航菜单 -->
  <view class="navigation-menu">
    <view class="nav-item" bindtap="navigateToVisit">
      <view class="nav-icon visit">📅</view>
      <text class="nav-label">参观预约</text>
    </view>
    <view class="nav-item" bindtap="navigateToGuide">
      <view class="nav-icon guide">🎧</view>
      <text class="nav-label">讲解预约</text>
    </view>
    <view class="nav-item" bindtap="navigateToEducation">
      <view class="nav-icon education">📚</view>
      <text class="nav-label">社教活动</text>
    </view>
    <view class="nav-item" bindtap="navigateToLecture">
      <view class="nav-icon lecture">🏛️</view>
      <text class="nav-label">国博讲堂</text>
    </view>
    <view class="nav-item" bindtap="navigateToResearch">
      <view class="nav-icon research">🔬</view>
      <text class="nav-label">研学活动</text>
    </view>
  </view>

  <!-- 当前展览 -->
  <view class="current-exhibitions">
    <view class="section-header">
      <text class="section-title">当前展览</text>
      <text class="view-all" bindtap="viewAllExhibitions">开幕式</text>
    </view>
    
    <view class="exhibitions-grid">
      <view class="exhibition-card" bindtap="viewLuminousExhibition">
        <view class="exhibition-image luminous">
          <view class="exhibition-content">
            <text class="exhibition-title-en">LUMINOUS</text>
            <text class="exhibition-title-zh">流光溢彩</text>
            <text class="exhibition-subtitle">酒器与饮酒艺术展</text>
          </view>
        </view>
      </view>
      
      <view class="exhibition-card" bindtap="viewStoneExhibition">
        <view class="exhibition-image stone">
          <view class="exhibition-content">
            <text class="exhibition-title-main">石韵天成</text>
            <text class="exhibition-subtitle-small">中国观赏石艺术展</text>
            <text class="exhibition-title-en-small">Natural Elegance</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>

/* pages/index/index.wxss */
.container {
  background: #f5f5f5;
  min-height: 100vh;
}

.custom-header {
  background: linear-gradient(135deg, #8B1538 0%, #A91B47 100%);
  color: white;
  padding: 12rpx 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.user-icon, .expand-icon {
  width: 48rpx;
  height: 48rpx;
  background: rgba(255,255,255,0.2);
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
}

.museum-title {
  text-align: center;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.title-main {
  font-size: 28rpx;
  font-weight: 500;
}

.title-sub {
  font-size: 20rpx;
  opacity: 0.8;
}

.header-right {
  display: flex;
  gap: 16rpx;
}

.menu-dots, .help-icon {
  width: 48rpx;
  height: 48rpx;
  background: rgba(255,255,255,0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}

.hero-section {
  background: linear-gradient(180deg, #8B1538 0%, #2D1810 100%);
  padding-bottom: 40rpx;
}

.book-swiper {
  height: 600rpx;
  padding: 40rpx 0;
}

.book-display {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.book-cover {
  width: 400rpx;
  height: 520rpx;
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 20rpx 60rpx rgba(0,0,0,0.3);
  display: flex;
  flex-direction: column;
  padding: 40rpx;
  position: relative;
  transform: perspective(1600rpx) rotateY(-10deg);
}

.book-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #8B1538;
  margin-bottom: 20rpx;
}

.book-subtitle-small {
  font-size: 28rpx;
  color: #8B1538;
  margin-bottom: 20rpx;
}

.book-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.book-number {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
}

.book-desc {
  font-size: 32rpx;
  color: #333;
  margin: 10rpx 0;
}

.book-main-title {
  font-size: 64rpx;
  font-weight: bold;
  color: #8B1538;
  line-height: 1.2;
}

.book-artifacts {
  margin-top: 30rpx;
}

.artifact-row {
  display: flex;
  gap: 20rpx;
  justify-content: center;
}

.artifact-image {
  width: 80rpx;
  height: 60rpx;
  background: #f0f0f0;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #666;
}

.navigation-menu {
  display: flex;
  justify-content: space-around;
  padding: 40rpx;
  background: white;
  margin-bottom: 20rpx;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.nav-item:active {
  transform: scale(0.95);
}

.nav-icon {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: white;
}

.nav-icon.visit { background: linear-gradient(135deg, #66BB6A, #4CAF50); }
.nav-icon.guide { background: linear-gradient(135deg, #FFA726, #FF9800); }
.nav-icon.education { background: linear-gradient(135deg, #AB47BC, #9C27B0); }
.nav-icon.lecture { background: linear-gradient(135deg, #FF8A65, #FF7043); }
.nav-icon.research { background: linear-gradient(135deg, #EF5350, #F44336); }

.nav-label {
  font-size: 24rpx;
  color: #666;
  text-align: center;
}

.current-exhibitions {
  padding: 40rpx;
  background: white;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  position: relative;
  padding-left: 20rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 8rpx;
  background: #8B1538;
  border-radius: 4rpx;
}

.view-all {
  color: #8B1538;
  font-size: 28rpx;
}

.exhibitions-grid {
  display: flex;
  gap: 30rpx;
}

.exhibition-card {
  flex: 1;
  background: white;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 24rpx rgba(0,0,0,0.1);
}

.exhibition-card:active {
  transform: translateY(-4rpx);
}

.exhibition-image {
  width: 100%;
  height: 240rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  position: relative;
}

.exhibition-image.luminous {
  background: linear-gradient(135deg, #4CAF50, #2E7D32);
}

.exhibition-image.stone {
  background: linear-gradient(135deg, #8D6E63, #5D4037);
}

.exhibition-content {
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.exhibition-title-en {
  font-size: 32rpx;
  font-weight: bold;
}

.exhibition-title-zh {
  font-size: 40rpx;
  font-weight: bold;
}

.exhibition-title-main {
  font-size: 64rpx;
  font-weight: bold;
}

.exhibition-subtitle {
  font-size: 24rpx;
  opacity: 0.9;
}

.exhibition-subtitle-small {
  font-size: 20rpx;
  opacity: 0.9;
}

.exhibition-title-en-small {
  font-size: 24rpx;
  opacity: 0.9;
}

// pages/index/index.js
Page({
  data: {
    currentSlide: 0
  },

  onLoad() {
    console.log('首页加载完成')
  },

  // 导航事件
  navigateToVisit() {
    wx.navigateTo({
      url: '/pages/visit/visit'
    })
  },

  navigateToGuide() {
    wx.navigateTo({
      url: '/pages/guide/guide'
    })
  },

  navigateToEducation() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },

  navigateToLecture() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },

  navigateToResearch() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },

  // 展览事件
  viewAllExhibitions() {
    wx.navigateTo({
      url: '/pages/exhibition/exhibition'
    })
  },

  viewLuminousExhibition() {
    wx.showToast({
      title: '流光溢彩展览',
      icon: 'none'
    })
  },

  viewStoneExhibition() {
    wx.showToast({
      title: '石韵天成展览',
      icon: 'none'
    })
  },

  onShareAppMessage() {
    return {
      title: '中国国家博物馆',
      path: '/pages/index/index'
    }
  }
})

// pages/visit/visit.wxml
<view class="container">
  <view class="page-header">
    <text class="page-title">参观预约</text>
  </view>
  
  <view class="content">
    <view class="info-card">
      <text class="card-title">预约须知</text>
      <view class="info-list">
        <text class="info-item">• 请提前1-7天进行预约</text>
        <text class="info-item">• 每日限流8000人次</text>
        <text class="info-item">• 免费参观，凭有效证件入馆</text>
        <text class="info-item">• 开放时间：9:00-17:00（16:00停止入馆）</text>
      </view>
    </view>
    
    <button class="btn-primary" bindtap="makeReservation">立即预约</button>
  </view>
</view>

/* pages/visit/visit.wxss */
.page-header {
  background: linear-gradient(135deg, #8B1538, #A91B47);
  padding: 40rpx;
  text-align: center;
}

.page-title {
  color: white;
  font-size: 36rpx;
  font-weight: bold;
}

.content {
  padding: 40rpx;
}

.info-card {
  background: white;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 24rpx rgba(0,0,0,0.1);
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.info-item {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

// pages/visit/visit.js
Page({
  makeReservation() {
    wx.showModal({
      title: '预约提示',
      content: '是否跳转到预约页面？',
      success(res) {
        if (res.confirm) {
          wx.showToast({
            title: '跳转预约系统',
            icon: 'success'
          })
        }
      }
    })
  }
})

// sitemap.json
{
  "desc": "关于本文件的更多信息，请参考文档 https://developers.weixin.qq.com/miniprogram/dev/framework/sitemap.html",
  "rules": [{
    "action": "allow",
    "page": "*"
  }]
}

// project.config.json
{
  "description": "中国国家博物馆小程序",
  "packOptions": {
    "ignore": []
  },
  "setting": {
    "urlCheck": true,
    "es6": true,
    "enhance": true,
    "postcss": true,
    "preloadBackgroundData": false,
    "minified": true,
    "newFeature": false,
    "coverView": true,
    "nodeModules": false,
    "autoAudits": false,
    "showShadowRootInWxmlPanel": true,
    "scopeDataCheck": false,
    "uglifyFileName": false,
    "checkInvalidKey": true,
    "checkSiteMap": true,
    "uploadWithSourceMap": true,
    "compileHotReLoad": false,
    "lazyloadPlaceholderEnable": false,
    "useMultiFrameRuntime": true,
    "useApiHook": true,
    "useApiHostProcess": true,
    "babelSetting": {
      "ignore": [],
      "disablePlugins": [],
      "outputPath": ""
    },
    "enableEngineNative": false,
    "useIsolateContext": true,
    "userConfirmedBundleSwitch": false,
    "packNpmManually": false,
    "packNpmRelationList": [],
    "minifyWXSS": true,
    "disableUseStrict": false,
    "minifyWXML": true,
    "showES6CompileOption": false,
    "useCompilerPlugins": false
  },
  "compileType": "miniprogram",
  "libVersion": "2.19.4",
  "appid": "your-appid-here",
  "projectname": "museum-miniprogram",
  "debugOptions": {
    "hidedInDevtools": []
  },
  "scripts": {},
  "staticServerOptions": {
    "baseURL": "",
    "servePath": ""
  },
  "isGameTourist": false,
  "condition": {
    "search": {
      "list": []
    },
    "conversation": {
      "list": []
    },
    "game": {
      "list": []
    },
    "plugin": {
      "list": []
    },
    "gamePlugin": {
      "list": []
    },
    "miniprogram": {
      "list": []
    }
  }
}